import scrapy
from datetime import datetime
from voyagr_scrapy.items import ProductItem
import re


class ProductSpider(scrapy.Spider):
    name = 'product'
    allowed_domains = ['mql5.com']
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(ProductSpider, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        item = ProductItem()
        item['url'] = response.url
        item['scraped_at'] = datetime.now().isoformat()
        item['market_id'] = 'mql5'

        # Core product information
        item['product_id'] = self.extract_product_id(response)
        item['name'] = self.extract_name(response)
        item['description'] = self.extract_description(response)
        item['price'] = self.extract_price(response)
        item['currency'] = self.extract_currency(response)
        item['category'] = self.extract_category(response)
        item['subcategory'] = self.extract_subcategory(response)

        # Developer/Vendor information
        item['developer_name'] = self.extract_developer_name(response)
        item['developer_id'] = self.extract_developer_id(response)
        item['vendor'] = self.extract_vendor(response)

        # Product details
        item['current_version'] = self.extract_current_version(response)
        item['last_update'] = self.extract_last_update(response)
        item['publish_date'] = self.extract_publish_date(response)
        item['images'] = self.extract_images(response)
        item['specifications'] = self.extract_specifications(response)

        # Statistics
        item['activation_count'] = self.extract_activation_count(response)
        item['demo_downloaded'] = self.extract_demo_downloaded(response)
        item['review_count'] = self.extract_review_count(response)
        item['comments_count'] = self.extract_comments_count(response)

        # Ratings
        item['rating_overall'] = self.extract_rating_overall(response)
        item['rating_description_quality'] = self.extract_rating_description_quality(response)
        item['rating_reliability'] = self.extract_rating_reliability(response)
        item['rating_support'] = self.extract_rating_support(response)

        # Legacy fields for backward compatibility
        item['rating'] = item['rating_overall']
        item['reviews_count'] = item['review_count']

        # Other fields
        item['availability'] = self.extract_availability(response)
        item['stock_quantity'] = self.extract_stock_quantity(response)
        item['brand'] = self.extract_brand(response)

        yield item

    def extract_product_id(self, response):
        """Extract product ID from URL like '/en/market/product/118805'"""
        url_match = re.search(r'/product/(\d+)', response.url)
        if url_match:
            return url_match.group(1)
        return response.url.split('/')[-1]

    def extract_name(self, response):
        """Extract product name from page title or main heading"""
        # First try h1 tag
        name = response.css('h1::text').get()
        if name:
            return name.strip()

        # Try title tag and clean it
        title = response.css('title::text').get()
        if title:
            # Remove " | Buy Trading Robot..." part
            if ' | ' in title:
                name = title.split(' | ')[0].strip()
                return name

        return None

    def extract_description(self, response):
        """Extract product description"""
        # Look for description in various containers
        description_parts = []
        desc_elements = response.css('.description, .product-description, .details, .overview')

        for element in desc_elements:
            text = ' '.join(element.css('::text').getall())
            if text and len(text.strip()) > 50:
                description_parts.append(text.strip())

        return '\n\n'.join(description_parts) if description_parts else None

    def extract_price(self, response):
        """Extract product price"""
        # Look for price in MQL5 format
        all_text = ' '.join(response.css('*::text').getall())
        # Look for price patterns like "50 USD" or "$50"
        price_match = re.search(r'(\d+(?:\.\d{2})?)\s*(USD|EUR|\$)', all_text)
        if price_match:
            return float(price_match.group(1))
        return None

    def extract_currency(self, response):
        """Extract currency"""
        all_text = ' '.join(response.css('*::text').getall())
        # Look for currency patterns
        currency_match = re.search(r'\d+(?:\.\d{2})?\s*(USD|EUR)', all_text)
        if currency_match:
            return currency_match.group(1)
        return 'USD'

    def extract_category(self, response):
        """Extract product category"""
        # Look in breadcrumbs or category links
        category = response.css('.breadcrumb li:last-child::text, .category::text').get()
        return category.strip() if category else None

    def extract_subcategory(self, response):
        """Extract product subcategory"""
        subcategory = response.css('.breadcrumb li:nth-last-child(2)::text, .subcategory::text').get()
        return subcategory.strip() if subcategory else None

    def extract_developer_name(self, response):
        """Extract developer/author name"""
        # Look for the developer link in the breadcrumb or product info
        developer_link = response.css('a[href*="/users/"]::text').get()
        if developer_link:
            return developer_link.strip()
        return None

    def extract_developer_id(self, response):
        """Extract developer ID from profile link"""
        developer_link = response.css('a[href*="/users/"]::attr(href)').get()
        if developer_link:
            # Extract username from URL like "/en/users/weredeu"
            id_match = re.search(r'/users/([^/?]+)', developer_link)
            if id_match:
                return id_match.group(1)
        return None

    def extract_current_version(self, response):
        """Extract current version from text like 'Version: 2.2'"""
        all_text = ' '.join(response.css('*::text').getall())
        version_match = re.search(r'Version:\s*(\d+\.?\d*)', all_text)
        if version_match:
            return version_match.group(1)
        return None

    def extract_last_update(self, response):
        """Extract last update date from text like 'Updated: 12 June 2025'"""
        all_text = ' '.join(response.css('*::text').getall())
        # Look for "Updated: DD Month YYYY" pattern
        update_match = re.search(r'Updated:\s*(\d{1,2}\s+\w+\s+\d{4})', all_text)
        if update_match:
            # Convert to YYYY-MM-DD format
            date_str = update_match.group(1)
            try:
                # Parse date like "12 June 2025"
                parsed_date = datetime.strptime(date_str, '%d %B %Y')
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                return date_str  # Return original if parsing fails
        return None

    def extract_publish_date(self, response):
        """Extract publish date from text like 'Published: 21 June 2024'"""
        all_text = ' '.join(response.css('*::text').getall())
        # Look for "Published: DD Month YYYY" pattern
        publish_match = re.search(r'Published:\s*(\d{1,2}\s+\w+\s+\d{4})', all_text)
        if publish_match:
            # Convert to YYYY-MM-DD format
            date_str = publish_match.group(1)
            try:
                # Parse date like "21 June 2024"
                parsed_date = datetime.strptime(date_str, '%d %B %Y')
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                return date_str  # Return original if parsing fails
        return None

    def extract_activation_count(self, response):
        """Extract activation count from text like 'Activations: 10'"""
        all_text = ' '.join(response.css('*::text').getall())
        activation_match = re.search(r'Activations:\s*(\d+)', all_text)
        if activation_match:
            return int(activation_match.group(1))
        return None

    def extract_demo_downloaded(self, response):
        """Extract demo download count from text like 'Demo downloaded: 9 318'"""
        all_text = ' '.join(response.css('*::text').getall())
        # Look for "Demo downloaded: X XXX" pattern
        demo_match = re.search(r'Demo downloaded:\s*([\d\s]+)', all_text)
        if demo_match:
            # Remove spaces and convert to int
            demo_count = demo_match.group(1).replace(' ', '')
            try:
                return int(demo_count)
            except ValueError:
                pass
        return None

    def extract_review_count(self, response):
        """Extract review count from text like 'Reviews (153)' or tab text"""
        all_text = ' '.join(response.css('*::text').getall())
        # Look for "Reviews (153)" pattern
        review_match = re.search(r'Reviews\s*\((\d+)\)', all_text)
        if review_match:
            return int(review_match.group(1))
        return None

    def extract_comments_count(self, response):
        """Extract comments count from text like 'Comments (203)' or tab text"""
        all_text = ' '.join(response.css('*::text').getall())
        # Look for "Comments (203)" pattern
        comment_match = re.search(r'Comments\s*\((\d+)\)', all_text)
        if comment_match:
            return int(comment_match.group(1))
        return None

    def extract_rating_overall(self, response):
        """Extract overall rating from text like '4.99'"""
        # Look for the rating number displayed prominently on the page
        all_text = ' '.join(response.css('*::text').getall())
        # Look for standalone rating numbers (like "4.99")
        rating_matches = re.findall(r'\b(\d+\.\d{2})\b', all_text)
        for rating in rating_matches:
            rating_val = float(rating)
            # Rating should be between 1 and 5
            if 1.0 <= rating_val <= 5.0:
                return rating_val
        return None

    def extract_rating_description_quality(self, response):
        """Extract description quality rating from data attributes or JavaScript"""
        return self._extract_detailed_rating(response, 0)  # First element in array

    def extract_rating_reliability(self, response):
        """Extract reliability rating from data attributes or JavaScript"""
        return self._extract_detailed_rating(response, 1)  # Second element in array

    def extract_rating_support(self, response):
        """Extract support rating from data attributes or JavaScript"""
        return self._extract_detailed_rating(response, 2)  # Third element in array

    def _extract_detailed_rating(self, response, index):
        """Look for detailed ratings in the specific JavaScript pattern:
        window.createRatingTooltip(['Description quality...','Reliability...','User support'], [5.0,5.0,5.0])
        """

        # 1. First check onmouseover attributes for the createRatingTooltip pattern
        onmouseover_elements = response.css('*[onmouseover]')
        for element in onmouseover_elements:
            onmouseover_value = element.css('::attr(onmouseover)').get()
            if onmouseover_value and 'createRatingTooltip' in onmouseover_value:
                # Look for the rating array pattern in createRatingTooltip
                tooltip_match = re.search(r'createRatingTooltip\([^,]+,\s*\[([^\]]+)\]\)', onmouseover_value)
                if tooltip_match:
                    try:
                        # Parse the rating values
                        values_str = tooltip_match.group(1)
                        values = [float(x.strip()) for x in values_str.split(',')]
                        if index < len(values):
                            return values[index]
                    except (ValueError, IndexError):
                        continue

        # 2. Look in JavaScript code for the createRatingTooltip pattern
        script_tags = response.css('script::text').getall()
        for script in script_tags:
            # Look for createRatingTooltip calls
            tooltip_matches = re.findall(r'createRatingTooltip\([^,]+,\s*\[([^\]]+)\]\)', script)
            for values_str in tooltip_matches:
                try:
                    values = [float(x.strip()) for x in values_str.split(',')]
                    if index < len(values) and len(values) >= 3:
                        return values[index]
                except (ValueError, IndexError):
                    continue

        # 3. Look in all HTML content for the pattern
        all_html = response.text
        tooltip_matches = re.findall(r'createRatingTooltip\([^,]+,\s*\[([^\]]+)\]\)', all_html)
        for values_str in tooltip_matches:
            try:
                values = [float(x.strip()) for x in values_str.split(',')]
                if index < len(values) and len(values) >= 3:
                    return values[index]
            except (ValueError, IndexError):
                continue

        return None

    # Legacy methods for backward compatibility
    def extract_vendor(self, response):
        """Extract vendor (same as developer_name)"""
        return self.extract_developer_name(response)

    def extract_availability(self, response):
        """Extract availability status"""
        return "Available"  # MQL5 products are generally available

    def extract_stock_quantity(self, response):
        """Extract stock quantity (not applicable for digital products)"""
        return None

    def extract_brand(self, response):
        """Extract brand (same as developer_name for MQL5)"""
        return self.extract_developer_name(response)

    def extract_images(self, response):
        """Extract product images"""
        images = response.css('.product-image img::attr(src), .gallery img::attr(src), img[src*="product"]::attr(src)').getall()
        # Convert relative URLs to absolute
        absolute_images = []
        for img in images:
            if img and not img.startswith('http'):
                img = response.urljoin(img)
            if img:
                absolute_images.append(img)
        return absolute_images if absolute_images else None

    def extract_specifications(self, response):
        """Extract product specifications"""
        specs = {}

        # Look for specification tables or lists
        spec_items = response.css('.specifications li, .specs tr, .product-details tr')
        for item in spec_items:
            key = item.css('.spec-name::text, td:first-child::text, th::text').get()
            value = item.css('.spec-value::text, td:last-child::text, td:nth-child(2)::text').get()
            if key and value:
                specs[key.strip()] = value.strip()

        # Also look for key-value pairs in the text
        all_text = ' '.join(response.css('*::text').getall())

        # Extract version, update date, etc. as specifications
        if self.extract_current_version(response):
            specs['Version'] = self.extract_current_version(response)
        if self.extract_last_update(response):
            specs['Last Update'] = self.extract_last_update(response)
        if self.extract_publish_date(response):
            specs['Publish Date'] = self.extract_publish_date(response)

        return specs if specs else None
