import scrapy
from datetime import datetime
from voyagr_scrapy.items import UserItem
import re


class MQL5<PERSON><PERSON><PERSON>pider(scrapy.Spider):
    name = 'mql5_user'
    allowed_domains = ['mql5.com']
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        item = UserItem()
        item['profile_url'] = response.url
        item['scraped_at'] = datetime.now().isoformat()
        item['market_id'] = 'mql5'

        # Basic user information
        item['user_id'] = self.extract_user_id(response)
        item['username'] = self.extract_username(response)
        item['display_name'] = self.extract_display_name(response)
        item['avatar_url'] = self.extract_avatar_url(response)
        item['location'] = self.extract_location(response)
        item['reputation_score'] = self.extract_reputation_score(response)

        # Developer/Business information (if available)
        item['company_name'] = self.extract_company_name(response)
        item['job_title'] = self.extract_job_title(response)
        item['website_url'] = self.extract_website_url(response)
        item['description'] = self.extract_description(response)
        item['broker_partnerships'] = self.extract_broker_partnerships(response)

        # Statistics
        item['experience_years'] = self.extract_experience_years(response)
        item['total_products'] = self.extract_total_products(response)
        item['total_demo_downloads'] = self.extract_demo_downloads(response)
        item['total_jobs'] = self.extract_total_jobs(response)
        item['total_signals'] = self.extract_total_signals(response)
        item['total_subscribers'] = self.extract_total_subscribers(response)

        # Rating information
        item['rating_value'] = self.extract_rating_value(response)
        item['rating_count'] = self.extract_rating_count(response)

        # Social information
        item['friends_count'] = self.extract_friends_count(response)
        item['friends_list'] = self.extract_friends_list(response)

        # Profile tabs availability
        item['has_seller_tab'] = self.check_seller_tab(response)
        item['has_publications_tab'] = self.check_publications_tab(response)
        item['has_achievements_tab'] = self.check_achievements_tab(response)
        item['has_algo_forge_tab'] = self.check_algo_forge_tab(response)

        # Activity information
        item['recent_posts'] = self.extract_recent_posts(response)
        item['post_count'] = self.extract_post_count(response)
        item['registration_date'] = self.extract_registration_date(response)

        yield item

    def extract_user_id(self, response):
        """Extract user ID from URL"""
        return response.url.split('/')[-1]

    def extract_username(self, response):
        """Extract username from URL or page"""
        return response.url.split('/')[-1]

    def extract_display_name(self, response):
        """Extract display name from page title or header"""
        # Try to get from page title first - format: "Name - username - Trader's profile"
        title = response.css('title::text').get()
        if title:
            # Extract name before first " - "
            name_match = re.search(r'^(.+?)\s*-\s*', title)
            if name_match:
                return name_match.group(1).strip()

        # Fallback to h1 or other selectors
        return response.css('h1::text').get()

    def extract_avatar_url(self, response):
        """Extract avatar/profile picture URL"""
        # MQL5 uses specific img structure for avatars
        avatar = response.css('img[alt*="big.jpg"], img[alt*="big.png"]::attr(src)').get()
        if not avatar:
            # Try general avatar patterns
            avatar = response.css('img[title*="avatar"], .avatar img::attr(src)').get()

        if avatar and not avatar.startswith('http'):
            avatar = response.urljoin(avatar)
        return avatar

    def extract_location(self, response):
        """Extract user location"""
        # MQL5 has location as a link to Google Maps
        location = response.css('a[href*="maps.google.com"]::text').get()
        return location

    def extract_reputation_score(self, response):
        """Extract reputation/rating score"""
        # Look for rating numbers in achievements link
        rating_text = response.css('a[href*="achievements"]::text').get()
        if rating_text:
            rating_match = re.search(r'(\d+)', rating_text)
            if rating_match:
                return int(rating_match.group(1))
        return None

    def extract_company_name(self, response):
        """Extract company name if user is a developer"""
        # Look for "CEO at [Company]" pattern in list items
        ceo_text = response.css('li:contains("CEO at")::text').get()
        if ceo_text:
            # Extract company name after "CEO at "
            company_match = re.search(r'CEO at\s+(.+)', ceo_text)
            if company_match:
                return company_match.group(1).strip()

        # Also check for company links
        company_link = response.css('li:contains("CEO at") a::text').get()
        return company_link

    def extract_job_title(self, response):
        """Extract job title"""
        # Look for job titles like "CEO at"
        title_element = response.css('li:contains("CEO at")::text').get()
        if title_element:
            title_match = re.search(r'^([^a]+)\s+at', title_element)
            if title_match:
                return title_match.group(1).strip()
        return None

    def extract_website_url(self, response):
        """Extract website URL"""
        # Look for website links in the profile
        website = response.css('li a[href*="www."]::attr(href)').get()
        if not website:
            # Try other patterns
            website = response.css('a[href^="http"]:not([href*="mql5.com"])::attr(href)').get()
        return website

    def extract_description(self, response):
        """Extract user/company description"""
        description_parts = []

        # Look for description in main content area
        # MQL5 puts descriptions in specific areas
        desc_paragraphs = response.css('p').getall()

        for p_html in desc_paragraphs:
            # Extract text from paragraph
            p_selector = scrapy.Selector(text=p_html)
            text = p_selector.css('::text').getall()
            if text:
                paragraph_text = ' '.join([t.strip() for t in text if t.strip()])
                if len(paragraph_text) > 100:  # Only substantial descriptions
                    description_parts.append(paragraph_text)

        return '\n\n'.join(description_parts) if description_parts else None

    def extract_broker_partnerships(self, response):
        """Extract broker partnership information"""
        partnerships = []

        # Look for specific broker mentions
        broker_links = response.css('a[href*="icmarkets.com"], a[href*="ictrading.com"]')
        for link in broker_links:
            href = link.css('::attr(href)').get()
            text = link.css('::text').get()
            if href and text:
                partnerships.append({
                    'name': text.strip(),
                    'url': href,
                    'type': 'broker'
                })

        return partnerships if partnerships else None

    def extract_experience_years(self, response):
        """Extract years of experience"""
        # Look for "X years experience" in the Information section
        info_text = response.css('div:contains("Information") ~ * ::text').getall()
        for text in info_text:
            if 'experience' in text.lower():
                exp_match = re.search(r'(\d+)', text)
                if exp_match:
                    return int(exp_match.group(1))
        return None

    def extract_total_products(self, response):
        """Extract total number of products"""
        # Look for products count in Information section
        info_text = response.css('div:contains("Information") ~ * ::text').getall()
        for text in info_text:
            if 'products' in text.lower():
                products_match = re.search(r'(\d+)', text)
                if products_match:
                    return int(products_match.group(1))
        return None

    def extract_demo_downloads(self, response):
        """Extract demo download count"""
        # Look for demo versions count in Information section
        info_text = response.css('div:contains("Information") ~ * ::text').getall()
        for text in info_text:
            if 'demo' in text.lower():
                demo_match = re.search(r'(\d+)', text)
                if demo_match:
                    return int(demo_match.group(1))
        return None

    def extract_total_jobs(self, response):
        """Extract total jobs count"""
        # Look for jobs count in Information section
        info_text = response.css('div:contains("Information") ~ * ::text').getall()
        for text in info_text:
            if 'jobs' in text.lower():
                jobs_match = re.search(r'(\d+)', text)
                if jobs_match:
                    return int(jobs_match.group(1))
        return None

    def extract_total_signals(self, response):
        """Extract total signals count"""
        # Look for signals count in Information section
        info_text = response.css('div:contains("Information") ~ * ::text').getall()
        for text in info_text:
            if 'signals' in text.lower():
                signals_match = re.search(r'(\d+)', text)
                if signals_match:
                    return int(signals_match.group(1))
        return None

    def extract_total_subscribers(self, response):
        """Extract total subscribers count"""
        # Look for subscribers count in Information section
        info_text = response.css('div:contains("Information") ~ * ::text').getall()
        for text in info_text:
            if 'subscribers' in text.lower():
                subs_match = re.search(r'(\d+)', text)
                if subs_match:
                    return int(subs_match.group(1))
        return None

    def extract_rating_value(self, response):
        """Extract rating value (e.g., 4.8)"""
        # Look for rating patterns like "4.8 (1301)"
        rating_text = response.css('strong::text').getall()
        for text in rating_text:
            rating_match = re.search(r'(\d+\.\d+)', text)
            if rating_match:
                return float(rating_match.group(1))
        return None

    def extract_rating_count(self, response):
        """Extract rating count (number of reviews)"""
        # Look for patterns like "4.8 (1301)"
        rating_text = response.css('strong::text').getall()
        for text in rating_text:
            count_match = re.search(r'\((\d+)\)', text)
            if count_match:
                return int(count_match.group(1))
        return None

    def extract_friends_count(self, response):
        """Extract friends count"""
        # Look for "Friends 10" pattern
        friends_text = response.css('*:contains("Friends")::text').get()
        if friends_text:
            friends_match = re.search(r'Friends\s+(\d+)', friends_text)
            if friends_match:
                return int(friends_match.group(1))
        return None

    def extract_friends_list(self, response):
        """Extract friends list"""
        friends = []
        # Look for friend links in the friends section
        friend_elements = response.css('a[href*="/users/"][title]')
        for friend in friend_elements:
            name = friend.css('::text').get()
            title = friend.css('::attr(title)').get()
            url = friend.css('::attr(href)').get()

            # Use title if available, otherwise use text
            display_name = title or name
            if display_name and url and '/users/' in url:
                friends.append({
                    'name': display_name.strip(),
                    'profile_url': response.urljoin(url),
                    'username': url.split('/')[-1]
                })

        return friends if friends else None

    def check_seller_tab(self, response):
        """Check if user has seller tab"""
        return bool(response.css('a[href*="/seller"]').get())

    def check_publications_tab(self, response):
        """Check if user has publications tab"""
        return bool(response.css('a[href*="/publications"]').get())

    def check_achievements_tab(self, response):
        """Check if user has achievements tab"""
        return bool(response.css('a[href*="/achievements"]').get())

    def check_algo_forge_tab(self, response):
        """Check if user has algo forge tab"""
        return bool(response.css('a[href*="forge.mql5.io"]').get())

    def extract_recent_posts(self, response):
        """Extract recent posts/activity"""
        posts = []

        # Look for user posts/messages - MQL5 has specific structure
        post_containers = response.css('div:contains("ago")')

        for container in post_containers[:5]:  # Limit to 5 recent posts
            # Extract post content
            post_text = container.css('::text').getall()
            if post_text:
                # Clean and join text
                clean_text = ' '.join([text.strip() for text in post_text if text.strip() and len(text.strip()) > 3])
                if len(clean_text) > 50:  # Only substantial posts
                    # Try to extract timestamp
                    timestamp = None
                    for text in post_text:
                        if 'ago' in text or '2025.' in text:
                            timestamp = text.strip()
                            break

                    posts.append({
                        'content': clean_text[:1000],  # Limit length
                        'timestamp': timestamp
                    })

        return posts if posts else None

    def extract_post_count(self, response):
        """Extract total post count if available"""
        # Count visible posts as approximation
        posts = self.extract_recent_posts(response)
        return len(posts) if posts else 0

    def extract_registration_date(self, response):
        """Extract registration date"""
        # Look for registration information - MQL5 format
        reg_text = response.css('*:contains("Registered at MQL5.community")').get()
        if reg_text:
            # Try to extract date from nearby elements
            date_text = response.css('*:contains("2025.") ::text').get()
            if date_text:
                date_match = re.search(r'(\d{4}\.\d{2}\.\d{2})', date_text)
                if date_match:
                    return date_match.group(1)
        return None
