import scrapy
from datetime import datetime
from voyagr_scrapy.items import UserItem
import re


class User<PERSON>pider(scrapy.Spider):
    name = 'user'
    allowed_domains = []
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(Use<PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        item = UserItem()
        item['profile_url'] = response.url
        item['scraped_at'] = datetime.now().isoformat()
        item['market_id'] = 'mql5'

        # Basic user information
        item['user_id'] = self.extract_user_id(response)
        item['username'] = self.extract_username(response)
        item['display_name'] = self.extract_display_name(response)
        item['avatar_url'] = self.extract_avatar_url(response)
        item['location'] = self.extract_location(response)
        item['reputation_score'] = self.extract_reputation_score(response)

        # Professional information (if available)
        item['company_name'] = self.extract_company_name(response)
        item['job_title'] = self.extract_job_title(response)
        item['website_url'] = self.extract_website_url(response)
        item['description'] = self.extract_description(response)
        item['broker_partnerships'] = self.extract_broker_partnerships(response)

        # Statistics
        item['experience_years'] = self.extract_experience_years(response)
        item['total_products'] = self.extract_total_products(response)
        item['total_demo_downloads'] = self.extract_demo_downloads(response)
        item['total_jobs'] = self.extract_total_jobs(response)
        item['total_signals'] = self.extract_total_signals(response)
        item['total_subscribers'] = self.extract_total_subscribers(response)

        # Rating information
        item['rating_value'] = self.extract_rating_value(response)
        item['rating_count'] = self.extract_rating_count(response)

        # Social information
        item['friends_count'] = self.extract_friends_count(response)
        item['friends_list'] = self.extract_friends_list(response)

        # Profile features
        item['has_seller_tab'] = self.check_seller_tab(response)
        item['has_publications_tab'] = self.check_publications_tab(response)
        item['has_achievements_tab'] = self.check_achievements_tab(response)
        item['has_algo_forge_tab'] = self.check_algo_forge_tab(response)

        # Activity information
        item['recent_posts'] = self.extract_recent_posts(response)
        item['post_count'] = self.extract_post_count(response)
        item['registration_date'] = self.extract_registration_date(response)

        # Determine account type
        item['account_type'] = self.determine_account_type(item)

        yield item

    def extract_user_id(self, response):
        """Extract user ID from URL"""
        return response.url.split('/')[-1]

    def extract_username(self, response):
        """Extract username from URL or page"""
        return response.url.split('/')[-1]

    def extract_display_name(self, response):
        """Extract display name from page title or header"""
        # Try to get from page title first - format: "Name - username - Trader's profile"
        title = response.css('title::text').get()
        if title:
            # Extract name before first " - "
            name_match = re.search(r'^(.+?)\s*-\s*', title)
            if name_match:
                return name_match.group(1).strip()

        # Fallback to h1 or other selectors
        return response.css('h1::text').get()

    def extract_avatar_url(self, response):
        """Extract avatar/profile picture URL"""
        # Look for the main profile image with _big in the src
        avatar = response.css('img[src*="_big.jpg"]::attr(src), img[src*="_big.png"]::attr(src)').get()

        if avatar and not avatar.startswith('http'):
            avatar = response.urljoin(avatar)
        return avatar

    def extract_location(self, response):
        """Extract user location"""
        # MQL5 has location as a link to Google Maps
        location = response.css('a[href*="maps.google.com"]::text').get()
        return location

    def extract_reputation_score(self, response):
        """Extract reputation/rating score"""
        # Look for the reputation score pattern like "34750" in the page
        all_text = ' '.join(response.css('*::text').getall())
        # Look for pattern like "Romania 34750 (1301)" - the number after location before rating
        reputation_match = re.search(r'Romania\s+(\d{4,})\s+\(', all_text)
        if reputation_match:
            return int(reputation_match.group(1))

        # Alternative pattern: look for large numbers near Romania
        alt_match = re.search(r'Romania\s+(\d{4,})', all_text)
        if alt_match:
            return int(alt_match.group(1))

        return None

    def extract_company_name(self, response):
        """Extract company name if user is a developer"""
        # Look for "CEO at [Company]" pattern in the page text
        all_text = ' '.join(response.css('*::text').getall())
        ceo_match = re.search(r'CEO\s+at\s+([A-Za-z\s]+)', all_text)
        if ceo_match:
            company = ceo_match.group(1).strip()
            # Clean up common issues
            if len(company) > 2:  # Avoid single letters
                return company

        # Look for company links
        company_link = response.css('a:contains("Incredible Traders")::text').get()
        if company_link:
            return company_link.strip()

        return None

    def extract_job_title(self, response):
        """Extract job title"""
        # Look for "CEO at" pattern
        all_text = ' '.join(response.css('*::text').getall())
        title_match = re.search(r'(CEO)\s+at\s+', all_text)
        if title_match:
            return title_match.group(1)
        return None

    def extract_website_url(self, response):
        """Extract website URL"""
        # Look for website URL pattern like "www.incredible-traders.com"
        all_text = ' '.join(response.css('*::text').getall())
        website_match = re.search(r'(www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', all_text)
        if website_match:
            website = website_match.group(1)
            if not website.startswith('http'):
                website = 'https://' + website
            return website
        return None

    def extract_description(self, response):
        """Extract user/company description"""
        # Look for the main description text after company info
        all_text = ' '.join(response.css('*::text').getall())

        # Look for description that starts after website and contains substantial content
        desc_match = re.search(r'www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\s+(.{200,}?)(?:\s+\d+\s+years|\s+MQL5\.community|$)', all_text, re.DOTALL)
        if desc_match:
            description = desc_match.group(1).strip()
            # Clean up the description
            description = re.sub(r'\s+', ' ', description)  # Normalize whitespace
            description = re.sub(r'[\\r\\n]+', ' ', description)  # Remove line breaks
            if len(description) > 100:  # Only return substantial descriptions
                return description

        return None

    def extract_broker_partnerships(self, response):
        """Extract broker partnership information"""
        partnerships = []

        # Look for specific broker mentions
        broker_links = response.css('a[href*="icmarkets.com"], a[href*="ictrading.com"]')
        for link in broker_links:
            href = link.css('::attr(href)').get()
            text = link.css('::text').get()
            if href and text:
                partnerships.append({
                    'name': text.strip(),
                    'url': href,
                    'type': 'broker'
                })

        return partnerships if partnerships else None

    def extract_experience_years(self, response):
        """Extract years of experience"""
        # Look for "X years experience" pattern in the page
        all_text = ' '.join(response.css('*::text').getall())
        exp_match = re.search(r'(\d+)\s+years?\s+experience', all_text, re.IGNORECASE)
        if exp_match:
            return int(exp_match.group(1))
        return None

    def extract_total_products(self, response):
        """Extract total number of products"""
        # Look for products count - format like "15 products"
        all_text = ' '.join(response.css('*::text').getall())
        products_match = re.search(r'(\d+)\s+products?', all_text, re.IGNORECASE)
        if products_match:
            return int(products_match.group(1))
        return None

    def extract_demo_downloads(self, response):
        """Extract demo download count"""
        # Look for demo versions count - format like "6868 demo versions"
        all_text = ' '.join(response.css('*::text').getall())
        demo_match = re.search(r'(\d+)\s+demo\s+versions?', all_text, re.IGNORECASE)
        if demo_match:
            return int(demo_match.group(1))
        return None

    def extract_total_jobs(self, response):
        """Extract total jobs count"""
        # Look for jobs count - format like "0 jobs"
        all_text = ' '.join(response.css('*::text').getall())
        jobs_match = re.search(r'(\d+)\s+jobs?', all_text, re.IGNORECASE)
        if jobs_match:
            return int(jobs_match.group(1))
        return None

    def extract_total_signals(self, response):
        """Extract total signals count"""
        # Look for signals count - format like "5 signals"
        all_text = ' '.join(response.css('*::text').getall())
        signals_match = re.search(r'(\d+)\s+signals?', all_text, re.IGNORECASE)
        if signals_match:
            return int(signals_match.group(1))
        return None

    def extract_total_subscribers(self, response):
        """Extract total subscribers count"""
        # Look for subscribers count - format like "8 subscribers"
        all_text = ' '.join(response.css('*::text').getall())
        subs_match = re.search(r'(\d+)\s+subscribers?', all_text, re.IGNORECASE)
        if subs_match:
            return int(subs_match.group(1))
        return None

    def extract_rating_value(self, response):
        """Extract rating value (e.g., 4.8)"""
        # Look for rating pattern like "4.8 (1301)" in the page
        all_text = ' '.join(response.css('*::text').getall())
        rating_match = re.search(r'(\d+\.\d+)\s*\(\d+\)', all_text)
        if rating_match:
            return float(rating_match.group(1))
        return None

    def extract_rating_count(self, response):
        """Extract rating count (number of reviews)"""
        # Look for pattern like "4.8 (1301)" in the page
        all_text = ' '.join(response.css('*::text').getall())
        count_match = re.search(r'\d+\.\d+\s*\((\d+)\)', all_text)
        if count_match:
            return int(count_match.group(1))
        return None

    def extract_friends_count(self, response):
        """Extract friends count"""
        # Look for "Friends 10" pattern
        friends_text = response.css('*:contains("Friends")::text').get()
        if friends_text:
            friends_match = re.search(r'Friends\s+(\d+)', friends_text)
            if friends_match:
                return int(friends_match.group(1))
        return None

    def extract_friends_list(self, response):
        """Extract friends list"""
        friends = []
        current_user = response.url.split('/')[-1]

        # Look for friend links - be more specific to avoid navigation links
        friend_elements = response.css('a[href*="/users/"][title]:not([href*="/achievements"]):not([href*="/seller"]):not([href*="/publications"])')

        for friend in friend_elements:
            name = friend.css('::text').get()
            title = friend.css('::attr(title)').get()
            url = friend.css('::attr(href)').get()

            if not url or not name:
                continue

            # Skip navigation and pagination links
            if any(skip in url for skip in ['/achievements', '/seller', '/publications', '/page', '#userActions']):
                continue

            # Skip if it's the current user
            username = url.split('/')[-1].split('?')[0].split('#')[0]
            if username == current_user:
                continue

            # Use title if available, otherwise use text
            display_name = title or name
            if display_name and len(display_name.strip()) > 1:  # Avoid single characters
                friends.append({
                    'name': display_name.strip(),
                    'profile_url': response.urljoin(url),
                    'username': username
                })

        return friends if friends else None

    def check_seller_tab(self, response):
        """Check if user has seller tab"""
        return bool(response.css('a[href*="/seller"]').get())

    def check_publications_tab(self, response):
        """Check if user has publications tab"""
        return bool(response.css('a[href*="/publications"]').get())

    def check_achievements_tab(self, response):
        """Check if user has achievements tab"""
        return bool(response.css('a[href*="/achievements"]').get())

    def check_algo_forge_tab(self, response):
        """Check if user has algo forge tab"""
        return bool(response.css('a[href*="forge.mql5.io"]').get())

    def extract_recent_posts(self, response):
        """Extract recent posts/activity"""
        posts = []

        # Look for user posts/messages - MQL5 has specific structure
        post_containers = response.css('div:contains("ago")')

        for container in post_containers[:5]:  # Limit to 5 recent posts
            # Extract post content
            post_text = container.css('::text').getall()
            if post_text:
                # Clean and join text
                clean_text = ' '.join([text.strip() for text in post_text if text.strip() and len(text.strip()) > 3])
                if len(clean_text) > 50:  # Only substantial posts
                    # Try to extract timestamp
                    timestamp = None
                    for text in post_text:
                        if 'ago' in text or '2025.' in text:
                            timestamp = text.strip()
                            break

                    posts.append({
                        'content': clean_text[:1000],  # Limit length
                        'timestamp': timestamp
                    })

        return posts if posts else None

    def extract_post_count(self, response):
        """Extract total post count if available"""
        # Count visible posts as approximation
        posts = self.extract_recent_posts(response)
        return len(posts) if posts else 0

    def extract_registration_date(self, response):
        """Extract registration date"""
        # Look for registration information - MQL5 format
        reg_text = response.css('*:contains("Registered at MQL5.community")').get()
        if reg_text:
            # Try to extract date from nearby elements
            date_text = response.css('*:contains("2025.") ::text').get()
            if date_text:
                date_match = re.search(r'(\d{4}\.\d{2}\.\d{2})', date_text)
                if date_match:
                    return date_match.group(1)
        return None

    def determine_account_type(self, item):
        """Determine account type based on extracted data"""
        if item.get('has_seller_tab') or item.get('total_products', 0) > 0:
            return 'developer'
        elif item.get('friends_count', 0) > 0:
            return 'regular'
        else:
            return 'basic'
