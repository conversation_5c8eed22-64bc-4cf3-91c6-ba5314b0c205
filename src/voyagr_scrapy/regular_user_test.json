[{"profile_url": "https://www.mql5.com/en/users/tomkoning", "scraped_at": "2025-06-13T17:25:01.482747", "market_id": "mql5", "user_id": "tom<PERSON>ing", "username": "tom<PERSON>ing", "display_name": "Massini28", "avatar_url": null, "location": "Netherlands", "reputation_score": null, "company_name": null, "job_title": null, "website_url": "https://www.mql5.com/en/articles/625?utm_source=www.mql5.com&utm_campaign=download", "description": null, "broker_partnerships": null, "experience_years": null, "total_products": null, "total_demo_downloads": null, "total_jobs": null, "total_signals": null, "total_subscribers": null, "rating_value": null, "rating_count": null, "friends_count": null, "friends_list": [{"name": "Rating", "profile_url": "https://www.mql5.com/en/users/tomkoning/achievements", "username": "achievements"}, {"name": "zahrn8989", "profile_url": "https://www.mql5.com/en/users/zahrn8989", "username": "zahrn8989"}, {"name": "<PERSON><PERSON><PERSON>", "profile_url": "https://www.mql5.com/en/users/weredeu", "username": "<PERSON><PERSON><PERSON>"}, {"name": "lordsky69", "profile_url": "https://www.mql5.com/en/users/lordsky69", "username": "lordsky69"}, {"name": "brandonautry", "profile_url": "https://www.mql5.com/en/users/brandonautry", "username": "brandonautry"}, {"name": "manolotrader", "profile_url": "https://www.mql5.com/en/users/manolotrader", "username": "manolotrader"}, {"name": "Trading_808", "profile_url": "https://www.mql5.com/en/users/trading_808", "username": "trading_808"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "profile_url": "https://www.mql5.com/en/users/cociulucian", "username": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "ESTARIX", "profile_url": "https://www.mql5.com/en/users/estarix", "username": "estarix"}, {"name": "<PERSON><PERSON><PERSON>", "profile_url": "https://www.mql5.com/en/users/pyrem", "username": "pyrem"}, {"name": "mateusz24052-", "profile_url": "https://www.mql5.com/en/users/mateusz24052-", "username": "mateusz24052-"}], "has_seller_tab": false, "has_publications_tab": true, "has_achievements_tab": true, "has_algo_forge_tab": true, "recent_posts": null, "post_count": 0, "registration_date": null}]