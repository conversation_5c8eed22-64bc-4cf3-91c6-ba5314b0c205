import scrapy
from scrapy import Item, Field


class MarketItem(scrapy.Item):
    market_id = Field()
    name = Field()
    description = Field()
    category = Field()
    location = Field()
    status = Field()
    created_date = Field()
    updated_date = Field()
    url = Field()
    scraped_at = Field()


class ProductItem(scrapy.Item):
    # Core product identification
    product_id = Field()
    name = Field()
    description = Field()
    url = Field()

    # Pricing & Availability
    price = Field()
    currency = Field()
    availability = Field()
    stock_quantity = Field()

    # Categorization
    category = Field()
    subcategory = Field()
    brand = Field()

    # Developer/Vendor info
    developer_name = Field()
    developer_id = Field()
    vendor = Field()

    # Product details
    current_version = Field()
    last_update = Field()
    publish_date = Field()
    images = Field()
    specifications = Field()

    # Statistics
    activation_count = Field()
    demo_downloaded = Field()
    review_count = Field()
    comments_count = Field()

    # Ratings
    rating_overall = Field()
    rating_description_quality = Field()
    rating_reliability = Field()
    rating_support = Field()

    # Legacy field (for backward compatibility)
    rating = Field()  # Same as rating_overall
    reviews_count = Field()  # Same as review_count

    # Metadata
    market_id = Field()
    scraped_at = Field()


class UserItem(scrapy.Item):
    # Core user identification
    user_id = Field()
    username = Field()
    display_name = Field()
    profile_url = Field()
    avatar_url = Field()

    # Basic profile info
    registration_date = Field()
    last_active = Field()
    location = Field()
    verified = Field()
    account_type = Field()

    # Reputation & Activity
    reputation_score = Field()
    rating_value = Field()  # e.g., 4.8
    rating_count = Field()  # e.g., 1301 reviews

    # Statistics
    total_reviews = Field()
    total_products = Field()
    total_demo_downloads = Field()
    total_jobs = Field()
    total_signals = Field()
    total_subscribers = Field()

    # Professional info (for developers/sellers)
    company_name = Field()
    job_title = Field()
    experience_years = Field()
    description = Field()
    website_url = Field()
    broker_partnerships = Field()

    # Social/Community
    friends_count = Field()
    friends_list = Field()
    social_links = Field()
    contact_info = Field()

    # Activity
    recent_posts = Field()
    post_count = Field()

    # Platform features
    has_seller_tab = Field()
    has_publications_tab = Field()
    has_achievements_tab = Field()
    has_algo_forge_tab = Field()

    # Metadata
    market_id = Field()
    scraped_at = Field()


# DeveloperItem removed - use UserItem instead with account_type = "developer"


class ReviewItem(scrapy.Item):
    review_id = Field()
    review_datetime = Field()
    reviewer_name = Field()
    reviewer_id = Field()
    reviewer_score = Field()
    review_content = Field()
    review_has_answer = Field()
    review_answer = Field()
    rating_description_quality = Field()
    rating_reliability = Field()
    rating_support = Field()
    timestamp_extraction_review = Field()
    product_id = Field()
    url = Field()
    scraped_at = Field()
