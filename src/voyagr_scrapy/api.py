from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from pydantic import BaseModel, HttpUrl
from typing import List, Optional, Dict, Any
import asyncio
import subprocess
import json
import os
from datetime import datetime
import uuid

app = FastAPI(
    title="Voyagr Scrapy API",
    description="API for running Scrapy spiders to extract market, product, user, and developer information",
    version="1.0.0"
)

class ScrapingRequest(BaseModel):
    spider_name: str
    start_urls: List[HttpUrl]
    allowed_domains: Optional[List[str]] = None
    custom_settings: Optional[Dict[str, Any]] = None

class ScrapingResponse(BaseModel):
    job_id: str
    status: str
    message: str

class JobStatus(BaseModel):
    job_id: str
    status: str
    spider_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    items_scraped: Optional[int] = None
    output_file: Optional[str] = None
    error_message: Optional[str] = None

# In-memory job storage (use database in production)
jobs_storage: Dict[str, JobStatus] = {}

AVAILABLE_SPIDERS = ['market', 'product', 'user', 'developer', 'product_view', 'review']

@app.get("/")
async def root():
    return {
        "message": "Voyagr Scrapy API",
        "available_endpoints": [
            "/spiders",
            "/scrape",
            "/jobs/{job_id}",
            "/jobs/{job_id}/results"
        ]
    }

@app.get("/spiders")
async def get_available_spiders():
    return {
        "available_spiders": AVAILABLE_SPIDERS,
        "descriptions": {
            "market": "Extract marketplace information",
            "product": "Extract product details and specifications",
            "user": "Extract user profiles and information",
            "developer": "Extract developer/vendor profiles"
        }
    }

@app.post("/scrape", response_model=ScrapingResponse)
async def start_scraping(request: ScrapingRequest, background_tasks: BackgroundTasks):
    if request.spider_name not in AVAILABLE_SPIDERS:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid spider name. Available spiders: {AVAILABLE_SPIDERS}"
        )

    job_id = str(uuid.uuid4())

    # Create job status
    job_status = JobStatus(
        job_id=job_id,
        status="starting",
        spider_name=request.spider_name,
        start_time=datetime.now()
    )
    jobs_storage[job_id] = job_status

    # Start scraping in background
    background_tasks.add_task(run_spider_task, job_id, request)

    return ScrapingResponse(
        job_id=job_id,
        status="started",
        message=f"Scraping job started for {request.spider_name} spider"
    )

@app.get("/jobs/{job_id}", response_model=JobStatus)
async def get_job_status(job_id: str):
    if job_id not in jobs_storage:
        raise HTTPException(status_code=404, detail="Job not found")

    return jobs_storage[job_id]

@app.get("/jobs/{job_id}/results")
async def get_job_results(job_id: str):
    if job_id not in jobs_storage:
        raise HTTPException(status_code=404, detail="Job not found")

    job = jobs_storage[job_id]

    if job.status != "completed":
        raise HTTPException(
            status_code=400,
            detail=f"Job is not completed yet. Current status: {job.status}"
        )

    if not job.output_file or not os.path.exists(job.output_file):
        raise HTTPException(status_code=404, detail="Results file not found")

    try:
        with open(job.output_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        return {
            "job_id": job_id,
            "items_count": len(results),
            "results": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading results: {str(e)}")

@app.delete("/jobs/{job_id}")
async def delete_job(job_id: str):
    if job_id not in jobs_storage:
        raise HTTPException(status_code=404, detail="Job not found")

    job = jobs_storage[job_id]

    # Remove output file if exists
    if job.output_file and os.path.exists(job.output_file):
        os.remove(job.output_file)

    # Remove from storage
    del jobs_storage[job_id]

    return {"message": f"Job {job_id} deleted"}

async def run_spider_task(job_id: str, request: ScrapingRequest):
    job = jobs_storage[job_id]

    try:
        job.status = "running"

        # Prepare command
        cmd = [
            "uv", "run", "scrapy", "crawl", request.spider_name,
            "-a", f"start_urls={','.join([str(url) for url in request.start_urls])}"
        ]

        if request.allowed_domains:
            cmd.extend(["-a", f"allowed_domains={','.join(request.allowed_domains)}"])

        # Set custom settings if provided
        if request.custom_settings:
            for key, value in request.custom_settings.items():
                cmd.extend(["-s", f"{key}={value}"])

        # Run the spider
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd="/Users/<USER>/ia_folder/voyagr_scrapy"
        )

        stdout, stderr = await process.communicate()

        if process.returncode == 0:
            job.status = "completed"
            job.end_time = datetime.now()

            # Find the output file
            output_dir = "output"
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.startswith(request.spider_name)]
                if files:
                    # Get the most recent file
                    latest_file = max(files, key=lambda x: os.path.getmtime(os.path.join(output_dir, x)))
                    job.output_file = os.path.join(output_dir, latest_file)

                    # Count items
                    try:
                        with open(job.output_file, 'r', encoding='utf-8') as f:
                            results = json.load(f)
                            job.items_scraped = len(results)
                    except:
                        job.items_scraped = 0
        else:
            job.status = "failed"
            job.end_time = datetime.now()
            job.error_message = stderr.decode() if stderr else "Unknown error"

    except Exception as e:
        job.status = "failed"
        job.end_time = datetime.now()
        job.error_message = str(e)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
