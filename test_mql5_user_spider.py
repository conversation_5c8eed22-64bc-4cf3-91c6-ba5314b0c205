#!/usr/bin/env python3
"""
Test script for MQL5 User Spider
"""

import sys
import os

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_spider():
    """Test the MQL5 user spider with both profile types"""
    
    # Test URLs
    developer_url = "https://www.mql5.com/en/users/weredeu"
    regular_user_url = "https://www.mql5.com/en/users/tomkoning"
    
    print("Testing MQL5 User Spider")
    print("=" * 50)
    
    print(f"\n1. Testing Developer Profile: {developer_url}")
    print(f"2. Testing Regular User Profile: {regular_user_url}")
    
    print("\nTo run the spider, use:")
    print(f"PYTHONPATH=/home/<USER>/perso/voyagr_scrapy/src uv run scrapy crawl mql5_user -a start_urls='{developer_url}' -o developer_profile.json")
    print(f"PYTHONPATH=/home/<USER>/perso/voyagr_scrapy/src uv run scrapy crawl mql5_user -a start_urls='{regular_user_url}' -o regular_user_profile.json")
    
    print("\nOr test both at once:")
    print(f"PYTHONPATH=/home/<USER>/perso/voyagr_scrapy/src uv run scrapy crawl mql5_user -a start_urls='{developer_url},{regular_user_url}' -o both_profiles.json")

if __name__ == "__main__":
    test_spider()
