# MQL5 User Spider Documentation

## Overview

The MQL5 User Spider (`mql5_user`) is a specialized Scrapy spider designed to extract comprehensive user profile information from MQL5.com. It intelligently handles both **developer profiles** (users with commercial activity) and **regular user profiles** (basic community members).

## Features

### ✅ **Unified Spider Approach**
- Single spider handles both developer and regular user profiles
- Intelligent field extraction that adapts based on available content
- Graceful handling of missing data (returns `null` for unavailable fields)

### ✅ **Comprehensive Data Extraction**

#### **Basic User Information**
- `user_id`: Username from URL
- `username`: Same as user_id
- `display_name`: Full name from page title
- `avatar_url`: Profile picture URL
- `location`: Geographic location (with Google Maps integration)
- `profile_url`: Full profile URL
- `market_id`: Always "mql5"
- `scraped_at`: ISO timestamp of extraction

#### **Developer-Specific Fields**
- `company_name`: Company name (if applicable)
- `job_title`: Job title (e.g., "CEO")
- `website_url`: Company/personal website
- `description`: Company/user description
- `broker_partnerships`: List of broker partnerships with URLs

#### **Statistics & Metrics**
- `experience_years`: Years of experience
- `total_products`: Number of products published
- `total_demo_downloads`: Demo download count
- `total_jobs`: Number of freelance jobs
- `total_signals`: Number of trading signals
- `total_subscribers`: Number of signal subscribers
- `reputation_score`: User reputation score
- `rating_value`: Average rating (e.g., 4.8)
- `rating_count`: Number of ratings received

#### **Social & Community**
- `friends_count`: Number of friends
- `friends_list`: List of friends with names and profile URLs
- `recent_posts`: Recent activity/posts with timestamps
- `post_count`: Number of recent posts found
- `registration_date`: Account registration date

#### **Profile Features**
- `has_seller_tab`: Boolean - has seller/marketplace tab
- `has_publications_tab`: Boolean - has publications tab
- `has_achievements_tab`: Boolean - has achievements tab
- `has_algo_forge_tab`: Boolean - has Algo Forge repository

## Usage Examples

### Single Developer Profile
```bash
PYTHONPATH=/home/<USER>/perso/voyagr_scrapy/src uv run scrapy crawl mql5_user \
  -a start_urls="https://www.mql5.com/en/users/weredeu" \
  -a allowed_domains="mql5.com" \
  -s LOG_LEVEL=INFO \
  -o developer_profile.json
```

### Single Regular User Profile
```bash
PYTHONPATH=/home/<USER>/perso/voyagr_scrapy/src uv run scrapy crawl mql5_user \
  -a start_urls="https://www.mql5.com/en/users/tomkoning" \
  -a allowed_domains="mql5.com" \
  -s LOG_LEVEL=INFO \
  -o regular_user_profile.json
```

### Multiple Profiles
```bash
PYTHONPATH=/home/<USER>/perso/voyagr_scrapy/src uv run scrapy crawl mql5_user \
  -a start_urls="https://www.mql5.com/en/users/weredeu,https://www.mql5.com/en/users/tomkoning" \
  -a allowed_domains="mql5.com" \
  -s LOG_LEVEL=INFO \
  -o multiple_profiles.json
```

## Sample Output

### Developer Profile (weredeu)
```json
{
  "profile_url": "https://www.mql5.com/en/users/weredeu",
  "display_name": "Bogdan Ion Puscasu",
  "location": "Romania",
  "experience_years": 2,
  "total_products": 15,
  "total_demo_downloads": 6869,
  "total_jobs": 0,
  "total_signals": 5,
  "total_subscribers": 8,
  "has_seller_tab": true,
  "broker_partnerships": [
    {
      "name": "https://icmarkets.com/?camp=18889",
      "url": "/go?link=https://icmarkets.com/?camp=18889",
      "type": "broker"
    }
  ]
}
```

### Regular User Profile (tomkoning)
```json
{
  "profile_url": "https://www.mql5.com/en/users/tomkoning",
  "display_name": "Massini28",
  "location": "Netherlands",
  "experience_years": null,
  "total_products": null,
  "total_demo_downloads": null,
  "total_jobs": null,
  "total_signals": null,
  "total_subscribers": null,
  "has_seller_tab": false,
  "friends_list": [
    {
      "name": "zahrn8989",
      "profile_url": "https://www.mql5.com/en/users/zahrn8989",
      "username": "zahrn8989"
    }
  ]
}
```

## Key Differences Between Profile Types

| Field | Developer Profile | Regular User Profile |
|-------|------------------|---------------------|
| `has_seller_tab` | `true` | `false` |
| `total_products` | Number (e.g., 15) | `null` |
| `total_demo_downloads` | Number (e.g., 6869) | `null` |
| `experience_years` | Number (e.g., 2) | `null` |
| `broker_partnerships` | Array of objects | `null` |
| `friends_list` | Usually empty/minimal | Rich friends data |
| `recent_posts` | Product promotions | Minimal activity |

## Technical Implementation

### Extraction Strategy
- **Regex-based text extraction**: Uses comprehensive regex patterns to find statistics
- **CSS selector fallbacks**: Multiple selector strategies for robustness
- **Intelligent null handling**: Returns `null` for unavailable data instead of errors
- **URL normalization**: Properly handles relative URLs

### Performance Optimizations
- **Single-pass extraction**: All data extracted in one page visit
- **Efficient text processing**: Combines all page text for regex matching
- **Minimal DOM traversal**: Optimized CSS selectors

## Files Created/Modified

1. **`src/voyagr_scrapy/items.py`** - Extended UserItem with MQL5-specific fields
2. **`src/voyagr_scrapy/spiders/mql5_user_spider.py`** - New specialized spider
3. **Test outputs** - Various JSON files demonstrating functionality

## Best Practices

1. **Always specify allowed_domains**: `"mql5.com"`
2. **Use appropriate log levels**: `INFO` for production, `DEBUG` for troubleshooting
3. **Handle rate limiting**: Add delays if scraping many profiles
4. **Validate output**: Check for null values in expected fields

## Future Enhancements

- [ ] Extract detailed rating breakdowns (description quality, reliability, support)
- [ ] Parse recent posts for structured data (product mentions, dates)
- [ ] Add pagination support for friends lists
- [ ] Extract achievement details
- [ ] Add support for other MQL5 languages (currently English only)

## Troubleshooting

- **Empty results**: Check if profile is public and accessible
- **Missing statistics**: Some users may not have developer-level data
- **Rate limiting**: Add `DOWNLOAD_DELAY` setting if needed
- **Encoding issues**: MQL5 uses UTF-8, ensure proper handling
